import React, { useState, useEffect } from 'react';
import {
  Con<PERSON>er,
  Card,
  Table,
  <PERSON>ton,
  Modal,
  Form,
  Alert,
  Spinner,
  Badge,
  Row,
  Col,
  Breadcrumb,
  InputGroup,
  Dropdown,
  OverlayTrigger,
  Tooltip,
  Tabs,
  Tab
} from 'react-bootstrap';
import {
  FaPlus,
  FaPencilAlt,
  FaTrashAlt,
  FaEye,
  FaHome,
  FaBox,
  FaSearch,
  FaFilter,
  FaTags,
  FaLayerGroup,
  FaSort,
  FaSortUp,
  FaSortDown
} from 'react-icons/fa';
import { fetchProducts, createProduct, updateProduct, deleteProduct, searchProducts } from '../../services/productService';
import { fetchBrands } from '../../services/brandService';
import { fetchCategories, fetchAllSousCategories, fetchAllSousSousCategories } from '../../services/categoryService';
import TablePagination from 'components/TablePagination';
import 'ui-component/extended/ProfessionalModal.css';

const ProductManagement = () => {
  // State management
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [brands, setBrands] = useState([]);
  const [categories, setCategories] = useState([]);
  const [sousCategories, setSousCategories] = useState([]);
  const [sousSousCategories, setSousSousCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [currentProduct, setCurrentProduct] = useState(null);
  const [productToDelete, setProductToDelete] = useState(null);
  const [activeTab, setActiveTab] = useState('basic'); // 'basic', 'images', 'attributes', 'variants'

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSousCategory, setSelectedSousCategory] = useState('');
  const [selectedSousSousCategory, setSelectedSousSousCategory] = useState('');
  const [stockFilter, setStockFilter] = useState(''); // 'in_stock', 'out_of_stock', ''
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [sortField, setSortField] = useState('nom_produit');
  const [sortDirection, setSortDirection] = useState('asc');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Form data
  const [formData, setFormData] = useState({
    nom_produit: '',
    description_produit: '',
    prix_produit: '',
    quantite_produit: '',
    reference: '',
    marque_id: '',
    categorie_id: '',
    sous_categorie_id: '',
    sous_sous_categorie_id: '',
    image_produit: null,
    attributs: []
  });

  // Image states - Multiple images support
  const [images, setImages] = useState([]);
  const [primaryImageIndex, setPrimaryImageIndex] = useState(0);

  // Attributes states
  const [availableAttributes, setAvailableAttributes] = useState([]);
  const [attributeValues, setAttributeValues] = useState({});

  // Variants states
  const [variants, setVariants] = useState([]);
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [newVariant, setNewVariant] = useState({
    sku: '',
    prix_supplement: 0,
    stock: 0,
    attributs: {}
  });

  // Filtered categories for modal hierarchy
  const [filteredSousCategories, setFilteredSousCategories] = useState([]);
  const [filteredSousSousCategories, setFilteredSousSousCategories] = useState([]);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Apply filters when dependencies change
  useEffect(() => {
    applyFilters();
  }, [
    products,
    searchTerm,
    selectedBrand,
    selectedCategory,
    selectedSousCategory,
    selectedSousSousCategory,
    stockFilter,
    priceRange,
    sortField,
    sortDirection
  ]);

  // Filter sous-categories when category changes in modal
  useEffect(() => {
    if (formData.categorie_id) {
      const filtered = sousCategories.filter((sc) => sc.categorie_id === parseInt(formData.categorie_id));
      setFilteredSousCategories(filtered);
    } else {
      setFilteredSousCategories([]);
    }
    // Reset dependent fields when category changes
    if (formData.categorie_id) {
      setFormData((prev) => ({ ...prev, sous_categorie_id: '', sous_sous_categorie_id: '' }));
      setFilteredSousSousCategories([]);
    }
  }, [formData.categorie_id, sousCategories]);

  // Filter sous-sous-categories when sous-category changes in modal
  useEffect(() => {
    if (formData.sous_categorie_id) {
      const filtered = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === parseInt(formData.sous_categorie_id));
      setFilteredSousSousCategories(filtered);
    } else {
      setFilteredSousSousCategories([]);
    }
    // Reset dependent field when sous-category changes
    if (formData.sous_categorie_id) {
      setFormData((prev) => ({ ...prev, sous_sous_categorie_id: '' }));
    }
  }, [formData.sous_categorie_id, sousSousCategories]);

  // Load attributes when sous-sous-category changes
  useEffect(() => {
    if (formData.sous_sous_categorie_id) {
      loadAttributesForCategory(formData.sous_sous_categorie_id);
    } else {
      setAvailableAttributes([]);
      setAttributeValues({});
    }
  }, [formData.sous_sous_categorie_id]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      setError('');

      console.log('🔄 Loading initial data...');
      console.log('🌐 API URLs being used:');
      console.log('- Products API:', import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api');
      console.log('- Environment:', import.meta.env.MODE);

      // Load data with individual error handling
      const results = await Promise.allSettled([
        fetchProducts(),
        fetchBrands(),
        fetchCategories(),
        fetchAllSousCategories(),
        fetchAllSousSousCategories()
      ]);

      const [productsRes, brandsRes, categoriesRes, sousCategoriesRes, sousSousCategoriesRes] = results;

      // Handle products
      if (productsRes.status === 'fulfilled') {
        const products = productsRes.value.data || productsRes.value;
        setProducts(Array.isArray(products) ? products : []);
        console.log('✅ Products loaded:', products.length);
        if (products.length > 0) {
          console.log('📋 Sample product structure:', products[0]);
        }
      } else {
        console.error('❌ Failed to load products:', productsRes.reason);
        setProducts([]);
      }

      // Handle brands
      if (brandsRes.status === 'fulfilled') {
        const brands = brandsRes.value.data || brandsRes.value;
        setBrands(Array.isArray(brands) ? brands : []);
        console.log('✅ Brands loaded:', brands.length);
      } else {
        console.error('❌ Failed to load brands:', brandsRes.reason);
        setBrands([]);
      }

      // Handle categories
      if (categoriesRes.status === 'fulfilled') {
        const categories = categoriesRes.value.data || categoriesRes.value;
        setCategories(Array.isArray(categories) ? categories : []);
        console.log('✅ Categories loaded:', categories.length);
        if (categories.length > 0) {
          console.log('📋 Sample category structure:', categories[0]);
        }
      } else {
        console.error('❌ Failed to load categories:', categoriesRes.reason);
        setCategories([]);
      }

      // Handle sous-categories
      if (sousCategoriesRes.status === 'fulfilled') {
        const sousCategories = sousCategoriesRes.value.data || sousCategoriesRes.value;
        setSousCategories(Array.isArray(sousCategories) ? sousCategories : []);
        console.log('✅ Sous-categories loaded:', sousCategories.length);
        if (sousCategories.length > 0) {
          console.log('📋 Sample sous-category structure:', sousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-categories:', sousCategoriesRes.reason);
        setSousCategories([]);
      }

      // Handle sous-sous-categories
      if (sousSousCategoriesRes.status === 'fulfilled') {
        const sousSousCategories = sousSousCategoriesRes.value.data || sousSousCategoriesRes.value;
        setSousSousCategories(Array.isArray(sousSousCategories) ? sousSousCategories : []);
        console.log('✅ Sous-sous-categories loaded:', sousSousCategories.length);
        if (sousSousCategories.length > 0) {
          console.log('📋 Sample sous-sous-category structure:', sousSousCategories[0]);
        }
      } else {
        console.error('❌ Failed to load sous-sous-categories:', sousSousCategoriesRes.reason);
        setSousSousCategories([]);
      }

      // Check if any critical data failed to load
      const failedRequests = results.filter((result) => result.status === 'rejected');
      if (failedRequests.length > 0) {
        const errorMessages = failedRequests.map((result) => result.reason?.message || 'Erreur inconnue');
        console.warn('⚠️ Some data failed to load:', errorMessages);

        // Only show error if products failed to load (critical)
        if (productsRes.status === 'rejected') {
          setError(`Impossible de charger les produits: ${productsRes.reason?.message || 'Erreur inconnue'}`);
        } else if (failedRequests.length === results.length) {
          setError(`Toutes les données ont échoué à charger. Vérifiez votre connexion.`);
        } else {
          setError(`Certaines données n'ont pas pu être chargées: ${errorMessages.join(', ')}`);
        }
      }

      console.log('✅ Initial data loading completed');
    } catch (err) {
      console.error('❌ Critical error loading data:', err);
      setError(`Erreur critique lors du chargement des données: ${err.message}`);

      // Fallback: Set empty arrays to prevent crashes
      setProducts([]);
      setBrands([]);
      setCategories([]);
      setSousCategories([]);
      setSousSousCategories([]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...products];

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (product) =>
          product.nom_produit?.toLowerCase().includes(term) ||
          product.reference?.toLowerCase().includes(term) ||
          product.description_produit?.toLowerCase().includes(term)
      );
    }

    // Brand filter
    if (selectedBrand) {
      filtered = filtered.filter((product) => product.marque_id === parseInt(selectedBrand));
    }

    // Category filters
    if (selectedSousSousCategory) {
      filtered = filtered.filter((product) => product.sous_sous_categorie_id === parseInt(selectedSousSousCategory));
    } else if (selectedSousCategory) {
      const sousSousIds = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === parseInt(selectedSousCategory)).map((ssc) => ssc.id);
      filtered = filtered.filter((product) => sousSousIds.includes(product.sous_sous_categorie_id));
    } else if (selectedCategory) {
      const sousIds = sousCategories.filter((sc) => sc.categorie_id === parseInt(selectedCategory)).map((sc) => sc.id);
      const sousSousIds = sousSousCategories.filter((ssc) => sousIds.includes(ssc.sous_categorie_id)).map((ssc) => ssc.id);
      filtered = filtered.filter((product) => sousSousIds.includes(product.sous_sous_categorie_id));
    }

    // Stock filter
    if (stockFilter === 'in_stock') {
      filtered = filtered.filter((product) => product.quantite_produit > 0);
    } else if (stockFilter === 'out_of_stock') {
      filtered = filtered.filter((product) => product.quantite_produit <= 0);
    }

    // Price range filter
    if (priceRange.min !== '') {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) >= parseFloat(priceRange.min));
    }
    if (priceRange.max !== '') {
      filtered = filtered.filter((product) => parseFloat(product.prix_produit) <= parseFloat(priceRange.max));
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle numeric fields
      if (sortField === 'prix_produit' || sortField === 'quantite_produit') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else {
        aValue = aValue?.toString().toLowerCase() || '';
        bValue = bValue?.toString().toLowerCase() || '';
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredProducts(filtered);
    setCurrentPage(1);
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="text-muted" />;
    return sortDirection === 'asc' ? <FaSortUp className="text-primary" /> : <FaSortDown className="text-primary" />;
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Multiple images handling
  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    const newImages = files.filter((f) => !images.some((img) => img.name === f.name));
    setImages((prev) => [...prev, ...newImages]);

    // If no primary selected, set the first as primary
    if (images.length === 0 && newImages.length > 0) {
      setPrimaryImageIndex(0);
    }
  };

  const removeImage = (index) => {
    setImages((prev) => prev.filter((_, i) => i !== index));

    // Adjust primary index if needed
    if (index === primaryImageIndex) {
      setPrimaryImageIndex(0);
    } else if (index < primaryImageIndex) {
      setPrimaryImageIndex((prev) => prev - 1);
    }
  };

  const setPrimaryImage = (index) => {
    setPrimaryImageIndex(index);
  };

  // Attributes handling
  const loadAttributesForCategory = async (sousSousCategorieId) => {
    if (!sousSousCategorieId) {
      setAvailableAttributes([]);
      return;
    }

    try {
      // This would need to be implemented in the API
      // const response = await fetch(`${API_URL}/attributs/sous-sous-categorie/${sousSousCategorieId}`);
      // const attributes = await response.json();
      // setAvailableAttributes(attributes.data || attributes);

      // For now, mock some common attributes
      const mockAttributes = [
        { id: 1, nom: 'Couleur', type_valeur: 'texte', obligatoire: true },
        { id: 2, nom: 'Taille', type_valeur: 'texte', obligatoire: false },
        { id: 3, nom: 'Matériau', type_valeur: 'texte', obligatoire: false },
        { id: 4, nom: 'Poids (kg)', type_valeur: 'nombre', obligatoire: false }
      ];
      setAvailableAttributes(mockAttributes);
    } catch (error) {
      console.error('Error loading attributes:', error);
      setAvailableAttributes([]);
    }
  };

  const handleAttributeChange = (attributId, value) => {
    setAttributeValues((prev) => ({
      ...prev,
      [attributId]: value
    }));
  };

  // Variants handling
  const addVariant = () => {
    if (!newVariant.sku) {
      setError('Le SKU est obligatoire pour la variante');
      return;
    }

    setVariants((prev) => [...prev, { ...newVariant, id: Date.now() }]);
    setNewVariant({
      sku: '',
      prix_supplement: 0,
      stock: 0,
      attributs: {}
    });
    setShowVariantModal(false);
  };

  const removeVariant = (index) => {
    setVariants((prev) => prev.filter((_, i) => i !== index));
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedBrand('');
    setSelectedCategory('');
    setSelectedSousCategory('');
    setSelectedSousSousCategory('');
    setStockFilter('');
    setPriceRange({ min: '', max: '' });
    setSortField('nom_produit');
    setSortDirection('asc');
  };

  const handleCreate = () => {
    setModalAction('create');
    setCurrentProduct(null);
    setFormData({
      nom_produit: '',
      description_produit: '',
      prix_produit: '',
      quantite_produit: '',
      reference: '',
      marque_id: '',
      categorie_id: '',
      sous_categorie_id: '',
      sous_sous_categorie_id: '',
      image_produit: null,
      attributs: []
    });

    // Reset all states
    setImages([]);
    setPrimaryImageIndex(0);
    setAvailableAttributes([]);
    setAttributeValues({});
    setVariants([]);
    setFilteredSousCategories([]);
    setFilteredSousSousCategories([]);
    setActiveTab('basic');
    setShowModal(true);
  };

  const handleEdit = (product) => {
    setModalAction('edit');
    setCurrentProduct(product);

    // Find the category hierarchy for this product
    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === product.sous_sous_categorie_id);
    const sousCategorie = sousSousCategorie ? sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id) : null;
    const categorie = sousCategorie ? categories.find((c) => c.id === sousCategorie.categorie_id) : null;

    setFormData({
      nom_produit: product.nom_produit || '',
      description_produit: product.description_produit || '',
      prix_produit: product.prix_produit || '',
      quantite_produit: product.quantite_produit || '',
      reference: product.reference || '',
      marque_id: product.marque_id || '',
      categorie_id: categorie?.id || '',
      sous_categorie_id: sousCategorie?.id || '',
      sous_sous_categorie_id: product.sous_sous_categorie_id || '',
      image_produit: null // For edit, we don't pre-load the image file
    });

    // Set filtered options based on current selection
    if (categorie) {
      const filteredSous = sousCategories.filter((sc) => sc.categorie_id === categorie.id);
      setFilteredSousCategories(filteredSous);
    }
    if (sousCategorie) {
      const filteredSousSous = sousSousCategories.filter((ssc) => ssc.sous_categorie_id === sousCategorie.id);
      setFilteredSousSousCategories(filteredSousSous);
    }

    setImages([]); // Reset images for edit
    setPrimaryImageIndex(0);
    setActiveTab('basic');
    setShowModal(true);
  };

  const handleDelete = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');

      // Prepare data for submission
      let dataToSend;

      // If there are images, use FormData, otherwise use JSON
      if (images.length > 0) {
        dataToSend = new FormData();

        // Add basic form data (excluding image_produit and attributs)
        const { image_produit, attributs, ...basicData } = formData;
        Object.keys(basicData).forEach((key) => {
          if (basicData[key] !== null && basicData[key] !== '') {
            dataToSend.append(key, basicData[key]);
          }
        });

        // Add images
        images.forEach((image, index) => {
          dataToSend.append('images[]', image);
          if (index === primaryImageIndex) {
            dataToSend.append('primary_image_index', index);
          }
        });

        // Add attributes if any
        if (Object.keys(attributeValues).length > 0) {
          dataToSend.append('attributs', JSON.stringify(attributeValues));
        }

        // Add variants if any
        if (variants.length > 0) {
          dataToSend.append('variants', JSON.stringify(variants));
        }
      } else {
        // Remove image_produit and attributs from data if no images
        const { image_produit, attributs, ...dataWithoutImage } = formData;
        dataToSend = {
          ...dataWithoutImage,
          attributs: attributeValues,
          variants: variants
        };
      }

      if (modalAction === 'create') {
        if (images.length > 0) {
          // Use fetch directly for FormData
          const response = await fetch(`${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/produits`, {
            method: 'POST',
            body: dataToSend
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erreur lors de la création du produit');
          }
        } else {
          await createProduct(dataToSend);
        }
        setSuccess('Produit créé avec succès');
      } else {
        if (images.length > 0) {
          // Use fetch directly for FormData with PUT method simulation
          dataToSend.append('_method', 'PUT');
          const response = await fetch(
            `${import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api'}/produits/${currentProduct.id}`,
            {
              method: 'POST', // Laravel uses POST with _method for file uploads
              body: dataToSend
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Erreur lors de la mise à jour du produit');
          }
        } else {
          await updateProduct(currentProduct.id, dataToSend);
        }
        setSuccess('Produit mis à jour avec succès');
      }

      setShowModal(false);
      setImages([]);
      setPrimaryImageIndex(0);
      setActiveTab('basic');
      await loadInitialData();
    } catch (err) {
      console.error('Error saving product:', err);
      setError(err.message || 'Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const confirmDelete = async () => {
    try {
      setLoading(true);
      await deleteProduct(productToDelete.id);
      setSuccess('Produit supprimé avec succès');
      setShowDeleteModal(false);
      await loadInitialData();
    } catch (err) {
      console.error('Error deleting product:', err);
      setError(err.message || 'Erreur lors de la suppression');
    } finally {
      setLoading(false);
    }
  };

  const getBrandName = (marqueId) => {
    const brand = brands.find((b) => b.id === marqueId);
    return brand ? brand.nom_marque : 'N/A';
  };

  const getCategoryPath = (sousSousCategorieId) => {
    if (!sousSousCategorieId) return 'N/A';

    const sousSousCategorie = sousSousCategories.find((ssc) => ssc.id === sousSousCategorieId);
    if (!sousSousCategorie) return 'N/A';

    // Handle both possible field names for sous-sous-categories
    const sousSousCatName = sousSousCategorie.nom || sousSousCategorie.nom_sous_sous_categorie;

    const sousCategorie = sousCategories.find((sc) => sc.id === sousSousCategorie.sous_categorie_id);
    if (!sousCategorie) return sousSousCatName || 'N/A';

    // Handle both possible field names for sous-categories
    const sousCatName = sousCategorie.nom || sousCategorie.nom_sous_categorie;

    const categorie = categories.find((c) => c.id === sousCategorie.categorie_id);
    if (!categorie) return `${sousCatName} > ${sousSousCatName}`;

    // Handle both possible field names for categories
    const catName = categorie.nom || categorie.nom_categorie;

    return `${catName} > ${sousCatName} > ${sousSousCatName}`;
  };

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProducts = filteredProducts.slice(startIndex, endIndex);

  if (loading && products.length === 0) {
    return (
      <Container className="py-4">
        <div className="text-center">
          <Spinner animation="border" variant="primary" />
          <p className="mt-2">Chargement des produits...</p>
        </div>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4">
        <Breadcrumb.Item href="#" onClick={() => window.history.back()}>
          <FaHome className="me-1" />
          Accueil
        </Breadcrumb.Item>
        <Breadcrumb.Item active>
          <FaBox className="me-1" />
          Gestion des Produits
        </Breadcrumb.Item>
      </Breadcrumb>

      {/* Alerts */}
      {error && (
        <Alert variant="danger" className="mb-4">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <strong>Erreur:</strong> {error}
            </div>
            <div>
              <Button variant="outline-danger" size="sm" onClick={loadInitialData} className="me-2">
                Réessayer
              </Button>
              <Button variant="outline-secondary" size="sm" onClick={() => setError('')}>
                Fermer
              </Button>
            </div>
          </div>
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess('')} dismissible className="mb-4">
          {success}
        </Alert>
      )}

      {/* Header */}
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Body>
          <Row className="align-items-center">
            <Col>
              <h2 className="mb-0 fw-bold text-primary">
                <FaBox className="me-2" />
                Gestion des Produits
              </h2>
              <p className="text-muted mb-0">Gérez tous vos produits en un seul endroit</p>
            </Col>
            <Col xs="auto">
              <Button variant="primary" onClick={handleCreate} className="d-flex align-items-center">
                <FaPlus className="me-2" />
                Ajouter un Produit
              </Button>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Filters */}
      <Card className="mb-4 border-0 shadow-sm">
        <Card.Header className="bg-light border-0">
          <h5 className="mb-0 d-flex align-items-center">
            <FaFilter className="me-2" />
            Filtres et Recherche
          </h5>
        </Card.Header>
        <Card.Body>
          <Row className="g-3">
            {/* Search */}
            <Col md={4}>
              <Form.Label className="fw-medium">Recherche</Form.Label>
              <InputGroup>
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Nom, référence, description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>

            {/* Brand Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Marque</Form.Label>
              <Form.Select value={selectedBrand} onChange={(e) => setSelectedBrand(e.target.value)}>
                <option value="">Toutes les marques</option>
                {brands.map((brand) => (
                  <option key={brand.id} value={brand.id}>
                    {brand.nom_marque}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Catégorie</Form.Label>
              <Form.Select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setSelectedSousCategory('');
                  setSelectedSousSousCategory('');
                }}
              >
                <option value="">Toutes les catégories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.nom || category.nom_categorie}
                  </option>
                ))}
              </Form.Select>
            </Col>

            {/* Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousCategory}
                onChange={(e) => {
                  setSelectedSousCategory(e.target.value);
                  setSelectedSousSousCategory('');
                }}
                disabled={!selectedCategory}
              >
                <option value="">Toutes</option>
                {sousCategories
                  .filter((sc) => sc.categorie_id === parseInt(selectedCategory))
                  .map((sousCategory) => (
                    <option key={sousCategory.id} value={sousCategory.id}>
                      {sousCategory.nom || sousCategory.nom_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>

            {/* Sous-Sous-Category Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Sous-sous-catégorie</Form.Label>
              <Form.Select
                value={selectedSousSousCategory}
                onChange={(e) => setSelectedSousSousCategory(e.target.value)}
                disabled={!selectedSousCategory}
              >
                <option value="">Toutes</option>
                {sousSousCategories
                  .filter((ssc) => ssc.sous_categorie_id === parseInt(selectedSousCategory))
                  .map((sousSousCategory) => (
                    <option key={sousSousCategory.id} value={sousSousCategory.id}>
                      {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                    </option>
                  ))}
              </Form.Select>
            </Col>
          </Row>

          <Row className="g-3 mt-2">
            {/* Stock Filter */}
            <Col md={2}>
              <Form.Label className="fw-medium">Stock</Form.Label>
              <Form.Select value={stockFilter} onChange={(e) => setStockFilter(e.target.value)}>
                <option value="">Tous</option>
                <option value="in_stock">En stock</option>
                <option value="out_of_stock">Rupture</option>
              </Form.Select>
            </Col>

            {/* Price Range */}
            <Col md={3}>
              <Form.Label className="fw-medium">Prix (€)</Form.Label>
              <Row>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Min"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange({ ...priceRange, min: e.target.value })}
                  />
                </Col>
                <Col xs="auto" className="d-flex align-items-center">
                  <span>-</span>
                </Col>
                <Col>
                  <Form.Control
                    type="number"
                    placeholder="Max"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange({ ...priceRange, max: e.target.value })}
                  />
                </Col>
              </Row>
            </Col>

            {/* Clear Filters */}
            <Col md={2} className="d-flex align-items-end">
              <Button variant="outline-secondary" onClick={clearFilters} className="w-100">
                Effacer les filtres
              </Button>
            </Col>

            {/* Results Count */}
            <Col md={5} className="d-flex align-items-end">
              <div className="text-muted">
                <strong>{filteredProducts.length}</strong> produit(s) trouvé(s) sur <strong>{products.length}</strong>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>

      {/* Products Table */}
      <Card className="border-0 shadow-sm">
        <Card.Body className="p-0">
          <div className="table-responsive">
            <Table hover className="mb-0">
              <thead className="bg-light">
                <tr>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('nom_produit')}>
                      Produit {getSortIcon('nom_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('reference')}>
                      Référence {getSortIcon('reference')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">Marque</th>
                  <th className="border-0 px-4 py-3">Catégorie</th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('prix_produit')}>
                      Prix {getSortIcon('prix_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3">
                    <Button variant="link" className="p-0 text-decoration-none fw-bold" onClick={() => handleSort('quantite_produit')}>
                      Stock {getSortIcon('quantite_produit')}
                    </Button>
                  </th>
                  <th className="border-0 px-4 py-3 text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentProducts.map((product) => (
                  <tr key={product.id}>
                    <td className="px-4 py-3">
                      <div>
                        <div className="fw-medium">{product.nom_produit}</div>
                        {product.description_produit && (
                          <small className="text-muted">{product.description_produit.substring(0, 50)}...</small>
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <code className="bg-light px-2 py-1 rounded">{product.reference || 'N/A'}</code>
                    </td>
                    <td className="px-4 py-3">{getBrandName(product.marque_id)}</td>
                    <td className="px-4 py-3">
                      <small className="text-muted">{getCategoryPath(product.sous_sous_categorie_id)}</small>
                    </td>
                    <td className="px-4 py-3">
                      <span className="fw-medium">{product.prix_produit ? `${product.prix_produit}€` : 'N/A'}</span>
                    </td>
                    <td className="px-4 py-3">
                      <Badge bg={product.quantite_produit > 0 ? 'success' : 'danger'} className="rounded-pill">
                        {product.quantite_produit > 0 ? `${product.quantite_produit} en stock` : 'Rupture'}
                      </Badge>
                    </td>
                    <td className="px-4 py-3 text-center">
                      <div className="d-flex justify-content-center gap-1">
                        <OverlayTrigger placement="top" overlay={<Tooltip>Modifier</Tooltip>}>
                          <Button variant="outline-primary" size="sm" onClick={() => handleEdit(product)}>
                            <FaPencilAlt />
                          </Button>
                        </OverlayTrigger>
                        <OverlayTrigger placement="top" overlay={<Tooltip>Supprimer</Tooltip>}>
                          <Button variant="outline-danger" size="sm" onClick={() => handleDelete(product)}>
                            <FaTrashAlt />
                          </Button>
                        </OverlayTrigger>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-5">
              <FaBox size={48} className="text-muted mb-3" />
              <h5 className="text-muted">Aucun produit trouvé</h5>
              <p className="text-muted">Essayez de modifier vos critères de recherche ou ajoutez un nouveau produit.</p>
            </div>
          )}
        </Card.Body>

        {/* Pagination */}
        {filteredProducts.length > 0 && (
          <Card.Footer className="bg-light border-0">
            <TablePagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
              totalItems={filteredProducts.length}
              itemsPerPage={itemsPerPage}
              startIndex={startIndex}
              endIndex={Math.min(endIndex, filteredProducts.length)}
              itemsPerPageOptions={[5, 10, 15, 25, 50]}
              showDirectPageInput={totalPages > 5}
              showPageInfo={true}
              showItemsPerPage={true}
            />
          </Card.Footer>
        )}
      </Card>

      {/* Create/Edit Product Modal */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg" className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold">
            {modalAction === 'create' ? (
              <>
                <FaPlus className="me-2 text-primary" />
                Ajouter un Produit
              </>
            ) : (
              <>
                <FaPencilAlt className="me-2 text-primary" />
                Modifier le Produit
              </>
            )}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body className="px-4">
            <Tabs activeKey={activeTab} onSelect={(k) => setActiveTab(k)} className="mb-3">
              {/* Basic Information Tab */}
              <Tab eventKey="basic" title="Informations de base">
                <Row className="g-3">
                  {/* Product Name */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Nom du produit <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="text"
                        value={formData.nom_produit}
                        onChange={(e) => setFormData({ ...formData, nom_produit: e.target.value })}
                        required
                        placeholder="Entrez le nom du produit"
                      />
                    </Form.Group>
                  </Col>

                  {/* Product Reference */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">Référence produit</Form.Label>
                      <Form.Control
                        type="text"
                        value={formData.reference}
                        onChange={(e) => setFormData({ ...formData, reference: e.target.value })}
                        placeholder="Référence unique"
                      />
                    </Form.Group>
                  </Col>

                  {/* Description */}
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-medium">Description</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        value={formData.description_produit}
                        onChange={(e) => setFormData({ ...formData, description_produit: e.target.value })}
                        placeholder="Description du produit"
                      />
                    </Form.Group>
                  </Col>

                  {/* Price */}
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Prix (€) <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="number"
                        step="0.01"
                        min="0"
                        value={formData.prix_produit}
                        onChange={(e) => setFormData({ ...formData, prix_produit: e.target.value })}
                        required
                        placeholder="0.00"
                      />
                    </Form.Group>
                  </Col>

                  {/* Quantity */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Quantité <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control
                        type="number"
                        min="0"
                        value={formData.quantite_produit}
                        onChange={(e) => setFormData({ ...formData, quantite_produit: e.target.value })}
                        required
                        placeholder="0"
                      />
                    </Form.Group>
                  </Col>

                  {/* Brand */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Marque <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        value={formData.marque_id}
                        onChange={(e) => setFormData({ ...formData, marque_id: e.target.value })}
                        required
                      >
                        <option value="">Sélectionnez une marque</option>
                        {brands.map((brand) => (
                          <option key={brand.id} value={brand.id}>
                            {brand.nom_marque}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        value={formData.categorie_id}
                        onChange={(e) => setFormData({ ...formData, categorie_id: e.target.value })}
                        required
                      >
                        <option value="">Sélectionnez une catégorie</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.nom || category.nom_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Sous-Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Sous-catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        value={formData.sous_categorie_id}
                        onChange={(e) => setFormData({ ...formData, sous_categorie_id: e.target.value })}
                        required
                        disabled={!formData.categorie_id}
                      >
                        <option value="">
                          {!formData.categorie_id ? "Sélectionnez d'abord une catégorie" : 'Sélectionnez une sous-catégorie'}
                        </option>
                        {filteredSousCategories.map((sousCategory) => (
                          <option key={sousCategory.id} value={sousCategory.id}>
                            {sousCategory.nom || sousCategory.nom_sous_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  {/* Sous-Sous-Category */}
                  <Col md={6}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Sous-sous-catégorie <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Select
                        value={formData.sous_sous_categorie_id}
                        onChange={(e) => setFormData({ ...formData, sous_sous_categorie_id: e.target.value })}
                        required
                        disabled={!formData.sous_categorie_id}
                      >
                        <option value="">
                          {!formData.sous_categorie_id ? "Sélectionnez d'abord une sous-catégorie" : 'Sélectionnez une sous-sous-catégorie'}
                        </option>
                        {filteredSousSousCategories.map((sousSousCategory) => (
                          <option key={sousSousCategory.id} value={sousSousCategory.id}>
                            {sousSousCategory.nom || sousSousCategory.nom_sous_sous_categorie}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>
              </Tab>

              {/* Images Tab */}
              <Tab eventKey="images" title="Images">
                <Row className="g-3">
                  <Col md={12}>
                    <Form.Group>
                      <Form.Label className="fw-medium">
                        Images du produit <span className="text-danger">*</span>
                      </Form.Label>
                      <Form.Control type="file" multiple accept="image/*" onChange={handleImagesChange} className="mb-3" />
                      <Form.Text className="text-muted">
                        Sélectionnez plusieurs images. Cliquez sur une image pour la définir comme principale.
                      </Form.Text>

                      {images.length > 0 && (
                        <div className="d-flex mt-3 flex-wrap gap-2">
                          {images.map((img, idx) => (
                            <div
                              key={idx}
                              className={`position-relative border rounded ${
                                idx === primaryImageIndex ? 'border-primary border-3' : 'border-secondary'
                              }`}
                              style={{ cursor: 'pointer', width: '120px', height: '120px' }}
                            >
                              <img
                                src={URL.createObjectURL(img)}
                                alt={`Image ${idx + 1}`}
                                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                className="rounded"
                                onClick={() => setPrimaryImage(idx)}
                              />
                              {idx === primaryImageIndex && (
                                <Badge bg="primary" className="position-absolute top-0 start-0 m-1">
                                  Principale
                                </Badge>
                              )}
                              <Button
                                variant="danger"
                                size="sm"
                                className="position-absolute top-0 end-0 m-1 rounded-circle"
                                style={{ width: '24px', height: '24px', padding: '0' }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeImage(idx);
                                }}
                              >
                                ×
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}

                      {images.length === 0 && (
                        <div className="text-center py-4 border rounded bg-light">
                          <FaBox size={32} className="text-muted mb-2" />
                          <p className="text-muted mb-0">Aucune image sélectionnée</p>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </Tab>

              {/* Attributes Tab */}
              <Tab eventKey="attributes" title="Attributs">
                <Row className="g-3">
                  {availableAttributes.length > 0 ? (
                    availableAttributes.map((attribute) => (
                      <Col md={6} key={attribute.id}>
                        <Form.Group>
                          <Form.Label className="fw-medium">
                            {attribute.nom}
                            {attribute.obligatoire && <span className="text-danger"> *</span>}
                          </Form.Label>
                          <Form.Control
                            type={attribute.type_valeur === 'nombre' ? 'number' : 'text'}
                            value={attributeValues[attribute.id] || ''}
                            onChange={(e) => handleAttributeChange(attribute.id, e.target.value)}
                            required={attribute.obligatoire}
                            placeholder={`Entrez ${attribute.nom.toLowerCase()}`}
                          />
                        </Form.Group>
                      </Col>
                    ))
                  ) : (
                    <Col md={12}>
                      <div className="text-center py-4">
                        <FaTags size={32} className="text-muted mb-2" />
                        <p className="text-muted mb-0">
                          {formData.sous_sous_categorie_id
                            ? 'Aucun attribut disponible pour cette catégorie'
                            : 'Sélectionnez une sous-sous-catégorie pour voir les attributs disponibles'}
                        </p>
                      </div>
                    </Col>
                  )}
                </Row>
              </Tab>

              {/* Variants Tab */}
              <Tab eventKey="variants" title="Variantes">
                <Row className="g-3">
                  <Col md={12}>
                    <div className="d-flex justify-content-between align-items-center mb-3">
                      <h6 className="mb-0">Variantes du produit</h6>
                      <Button variant="outline-primary" size="sm" onClick={() => setShowVariantModal(true)}>
                        <FaPlus className="me-1" />
                        Ajouter une variante
                      </Button>
                    </div>

                    {variants.length > 0 ? (
                      <Table striped bordered hover size="sm">
                        <thead>
                          <tr>
                            <th>SKU</th>
                            <th>Prix supplément</th>
                            <th>Stock</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {variants.map((variant, idx) => (
                            <tr key={variant.id}>
                              <td>{variant.sku}</td>
                              <td>{variant.prix_supplement}€</td>
                              <td>{variant.stock}</td>
                              <td>
                                <Button variant="outline-danger" size="sm" onClick={() => removeVariant(idx)}>
                                  <FaTrashAlt />
                                </Button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    ) : (
                      <div className="text-center py-4 border rounded bg-light">
                        <FaLayerGroup size={32} className="text-muted mb-2" />
                        <p className="text-muted mb-0">Aucune variante créée</p>
                        <small className="text-muted">Les variantes permettent de créer différentes versions du même produit</small>
                      </div>
                    )}
                  </Col>
                </Row>
              </Tab>
            </Tabs>
          </Modal.Body>
          <Modal.Footer className="border-0 pt-0">
            <Button variant="outline-secondary" onClick={() => setShowModal(false)} disabled={loading}>
              Annuler
            </Button>
            <Button type="submit" variant="primary" disabled={loading}>
              {loading ? (
                <>
                  <Spinner animation="border" size="sm" className="me-2" />
                  {modalAction === 'create' ? 'Création...' : 'Modification...'}
                </>
              ) : (
                <>
                  {modalAction === 'create' ? (
                    <>
                      <FaPlus className="me-2" />
                      Créer le produit
                    </>
                  ) : (
                    <>
                      <FaPencilAlt className="me-2" />
                      Modifier le produit
                    </>
                  )}
                </>
              )}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)} className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold text-danger">
            <FaTrashAlt className="me-2" />
            Confirmer la suppression
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="px-4">
          <div className="text-center py-3">
            <FaTrashAlt size={48} className="text-danger mb-3" />
            <h5>Êtes-vous sûr de vouloir supprimer ce produit ?</h5>
            {productToDelete && (
              <div className="mt-3">
                <p className="mb-1">
                  <strong>Produit :</strong> {productToDelete.nom_produit}
                </p>
                <p className="mb-1">
                  <strong>Référence :</strong> {productToDelete.reference || 'N/A'}
                </p>
                <p className="text-muted mb-0">Cette action est irréversible.</p>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="outline-secondary" onClick={() => setShowDeleteModal(false)} disabled={loading}>
            Annuler
          </Button>
          <Button variant="danger" onClick={confirmDelete} disabled={loading}>
            {loading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                Suppression...
              </>
            ) : (
              <>
                <FaTrashAlt className="me-2" />
                Supprimer
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Variant Modal */}
      <Modal show={showVariantModal} onHide={() => setShowVariantModal(false)} className="professional-modal">
        <Modal.Header closeButton className="border-0 pb-0">
          <Modal.Title className="fw-bold">
            <FaPlus className="me-2 text-primary" />
            Ajouter une Variante
          </Modal.Title>
        </Modal.Header>
        <Modal.Body className="px-4">
          <Row className="g-3">
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">
                  SKU <span className="text-danger">*</span>
                </Form.Label>
                <Form.Control
                  type="text"
                  value={newVariant.sku}
                  onChange={(e) => setNewVariant({ ...newVariant, sku: e.target.value })}
                  placeholder="Ex: PROD-001-RED-L"
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">Prix supplément (€)</Form.Label>
                <Form.Control
                  type="number"
                  step="0.01"
                  value={newVariant.prix_supplement}
                  onChange={(e) => setNewVariant({ ...newVariant, prix_supplement: parseFloat(e.target.value) || 0 })}
                  placeholder="0.00"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group>
                <Form.Label className="fw-medium">Stock</Form.Label>
                <Form.Control
                  type="number"
                  min="0"
                  value={newVariant.stock}
                  onChange={(e) => setNewVariant({ ...newVariant, stock: parseInt(e.target.value) || 0 })}
                  placeholder="0"
                />
              </Form.Group>
            </Col>

            {/* Variant Attributes */}
            {availableAttributes.length > 0 && (
              <Col md={12}>
                <hr />
                <h6>Attributs de la variante</h6>
                <Row className="g-2">
                  {availableAttributes.map((attribute) => (
                    <Col md={6} key={attribute.id}>
                      <Form.Group>
                        <Form.Label className="fw-medium">{attribute.nom}</Form.Label>
                        <Form.Control
                          type={attribute.type_valeur === 'nombre' ? 'number' : 'text'}
                          value={newVariant.attributs[attribute.id] || ''}
                          onChange={(e) =>
                            setNewVariant({
                              ...newVariant,
                              attributs: { ...newVariant.attributs, [attribute.id]: e.target.value }
                            })
                          }
                          placeholder={`${attribute.nom} de cette variante`}
                        />
                      </Form.Group>
                    </Col>
                  ))}
                </Row>
              </Col>
            )}
          </Row>
        </Modal.Body>
        <Modal.Footer className="border-0 pt-0">
          <Button variant="outline-secondary" onClick={() => setShowVariantModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={addVariant}>
            <FaPlus className="me-2" />
            Ajouter la variante
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ProductManagement;
